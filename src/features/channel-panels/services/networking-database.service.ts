import { Injectable, Logger } from '@nestjs/common';

import {
    businessOpportunities, DatabaseService, jobApplications,
    jobPostings,
    opportunityInterests,
    professionalConnections,
    professionalProfiles,
    skillEndorsements, users, type BusinessOpportunity,
    type JobApplication,
    type JobPosting,
    type NewBusinessOpportunity,
    type NewJobPosting,
    type NewProfessionalProfile,
    type ProfessionalConnection,
    type ProfessionalProfile,
    type SkillEndorsement
} from '@/core/database';
// Redis operations - no need for SQL query builders

export interface ProfileWithStats extends ProfessionalProfile {
  mutualConnections?: number;
  recentEndorsements?: SkillEndorsement[];
  skillEndorsementCounts?: Record<string, number>;
}

export interface JobWithStats extends JobPosting {
  applicantCount?: number;
  viewCount?: number;
  isApplied?: boolean;
}

export interface NetworkingMetrics {
  totalProfiles: number;
  totalConnections: number;
  totalJobs: number;
  totalApplications: number;
  totalOpportunities: number;
  activeRecruiters: number;
  popularSkills: Array<{ skill: string; count: number }>;
  industryBreakdown: Array<{ industry: string; count: number }>;
}

@Injectable()
export class NetworkingDatabaseService {
  private readonly logger = new Logger(NetworkingDatabaseService.name);

  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.getDb();
  }

  // Professional Profile Management
  async createProfile(profileData: Omit<NewProfessionalProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProfessionalProfile> {
    try {
      const [profile] = await this.db.insert(professionalProfiles)
        .values(profileData)
        .returning();
      
      this.logger.log(`Created professional profile: ${profile.displayName} (${profile.id})`);
      return profile;
    } catch (error) {
      this.logger.error('Failed to create profile:', error);
      throw error;
    }
  }

  async updateProfile(userId: string, updates: Partial<NewProfessionalProfile>): Promise<{ success: boolean; message: string }> {
    try {
      await this.db
        .update(professionalProfiles)
        .set(updates)
        .where(eq(professionalProfiles.userId, userId));

      this.logger.log(`Updated profile for user ${userId}`);
      return { success: true, message: 'Profile updated successfully!' };
    } catch (error) {
      this.logger.error('Failed to update profile:', error);
      return { success: false, message: 'Failed to update profile. Please try again.' };
    }
  }

  async getProfile(userId: string): Promise<ProfileWithStats | null> {
    try {
      const [profile] = await this.db
        .select()
        .from(professionalProfiles)
        .where(eq(professionalProfiles.userId, userId))
        .limit(1);

      if (!profile) return null;

      // Get endorsement counts for skills
      const endorsements = await this.db
        .select({
          skill: skillEndorsements.skill,
          count: sql<number>`COUNT(*)`
        })
        .from(skillEndorsements)
        .where(eq(skillEndorsements.profileId, userId))
        .groupBy(skillEndorsements.skill);

      const skillEndorsementCounts = endorsements.reduce((acc, e) => {
        acc[e.skill] = e.count;
        return acc;
      }, {} as Record<string, number>);

      // Get recent endorsements
      const recentEndorsements = await this.db
        .select()
        .from(skillEndorsements)
        .where(eq(skillEndorsements.profileId, userId))
        .orderBy(desc(skillEndorsements.createdAt))
        .limit(5);

      return {
        ...profile,
        skillEndorsementCounts,
        recentEndorsements
      };
    } catch (error) {
      this.logger.error('Failed to get profile:', error);
      throw error;
    }
  }

  async searchProfiles(
    guildId: string,
    filters?: {
      industry?: string;
      skills?: string[];
      experience?: string;
      location?: string;
      availableForHire?: boolean;
    },
    searchTerm?: string,
    limit: number = 20
  ): Promise<ProfileWithStats[]> {
    try {
      let whereConditions = [eq(professionalProfiles.guildId, guildId)];
      
      if (filters?.industry) {
        whereConditions.push(eq(professionalProfiles.industry, filters.industry));
      }
      if (filters?.experience) {
        whereConditions.push(eq(professionalProfiles.experience, filters.experience));
      }
      if (filters?.location) {
        whereConditions.push(ilike(professionalProfiles.location, `%${filters.location}%`));
      }
      if (filters?.availableForHire !== undefined) {
        whereConditions.push(eq(professionalProfiles.isAvailableForHire, filters.availableForHire));
      }

      if (searchTerm) {
        whereConditions.push(sql`(
          ${professionalProfiles.displayName} ILIKE ${'%' + searchTerm + '%'} OR
          ${professionalProfiles.title} ILIKE ${'%' + searchTerm + '%'} OR
          ${professionalProfiles.bio} ILIKE ${'%' + searchTerm + '%'} OR
          ${professionalProfiles.company} ILIKE ${'%' + searchTerm + '%'}
        )`);
      }

      if (filters?.skills && filters.skills.length > 0) {
        whereConditions.push(sql`${professionalProfiles.skills}::jsonb ?| array[${filters.skills.map((s: any) => `'${s}'`).join(',')}]`);
      }

      return await this.db
        .select()
        .from(professionalProfiles)
        .where(and(...whereConditions))
        .orderBy(desc(professionalProfiles.connectionCount), desc(professionalProfiles.endorsementCount))
        .limit(limit) as ProfileWithStats[];
    } catch (error) {
      this.logger.error('Failed to search profiles:', error);
      throw error;
    }
  }

  // Connection Management
  async sendConnectionRequest(
    requesterId: string,
    receiverId: string,
    message?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if connection already exists
      const existing = await this.db
        .select()
        .from(professionalConnections)
        .where(and(
          sql`(
            (${professionalConnections.requesterId} = ${requesterId} AND ${professionalConnections.receiverId} = ${receiverId}) OR
            (${professionalConnections.requesterId} = ${receiverId} AND ${professionalConnections.receiverId} = ${requesterId})
          )`
        ))
        .limit(1);

      if (existing.length > 0) {
        return { success: false, message: 'Connection request already exists!' };
      }

      await this.db.insert(professionalConnections).values({
        requesterId,
        receiverId,
        message,
        status: 'pending'
      } as any);

      this.logger.log(`Connection request sent from ${requesterId} to ${receiverId}`);
      return { success: true, message: 'Connection request sent successfully!' };
    } catch (error) {
      this.logger.error('Failed to send connection request:', error);
      return { success: false, message: 'Failed to send connection request. Please try again.' };
    }
  }

  async respondToConnection(
    connectionId: number,
    response: 'accepted' | 'rejected'
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.db
        .update(professionalConnections)
        .set({
          status: response,
          acceptedAt: response === 'accepted' ? new Date() : undefined
        } as any)
        .where(eq(professionalConnections.id, connectionId));

      if (response === 'accepted') {
        // Update connection counts
        const [connection] = await this.db
          .select()
          .from(professionalConnections)
          .where(eq(professionalConnections.id, connectionId))
          .limit(1);

        if (connection) {
          await this.db
            .update(professionalProfiles)
            .set({
              connectionCount: sql`${professionalProfiles.connectionCount} + 1`
            } as any)
            .where(inArray(professionalProfiles.userId, [connection.requesterId, connection.receiverId]));
        }
      }

      this.logger.log(`Connection ${connectionId} ${response}`);
      return { success: true, message: `Connection ${response} successfully!` };
    } catch (error) {
      this.logger.error('Failed to respond to connection:', error);
      return { success: false, message: 'Failed to respond to connection. Please try again.' };
    }
  }

  async getUserConnections(userId: string, status?: string): Promise<ProfessionalConnection[]> {
    try {
      let whereConditions = [
        sql`(${professionalConnections.requesterId} = ${userId} OR ${professionalConnections.receiverId} = ${userId})`
      ];
      
      if (status) {
        whereConditions.push(eq(professionalConnections.status, status));
      }

      return await this.db
        .select()
        .from(professionalConnections)
        .where(and(...whereConditions))
        .orderBy(desc(professionalConnections.createdAt));
    } catch (error) {
      this.logger.error('Failed to get user connections:', error);
      throw error;
    }
  }

  // Skill Endorsement Management
  async endorseSkill(
    profileId: string,
    endorserId: string,
    skill: string,
    message?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already endorsed
      const existing = await this.db
        .select()
        .from(skillEndorsements)
        .where(and(
          eq(skillEndorsements.profileId, profileId),
          eq(skillEndorsements.endorserId, endorserId),
          eq(skillEndorsements.skill, skill)
        ))
        .limit(1);

      if (existing.length > 0) {
        return { success: false, message: 'You have already endorsed this skill!' };
      }

      await this.db.insert(skillEndorsements).values({
        profileId,
        endorserId,
        skill,
        message
      } as any);

      // Update endorsement count
      await this.db
        .update(professionalProfiles)
        .set({
          endorsementCount: sql`${professionalProfiles.endorsementCount} + 1`
        } as any)
        .where(eq(professionalProfiles.userId, profileId));

      this.logger.log(`${endorserId} endorsed ${skill} for ${profileId}`);
      return { success: true, message: 'Skill endorsed successfully!' };
    } catch (error) {
      this.logger.error('Failed to endorse skill:', error);
      return { success: false, message: 'Failed to endorse skill. Please try again.' };
    }
  }

  // Job Management
  async createJob(jobData: Omit<NewJobPosting, 'id' | 'createdAt' | 'updatedAt'>): Promise<JobPosting> {
    try {
      const [job] = await this.db.insert(jobPostings)
        .values(jobData)
        .returning();
      
      this.logger.log(`Created job posting: ${job.title} (${job.id})`);
      return job;
    } catch (error) {
      this.logger.error('Failed to create job:', error);
      throw error;
    }
  }

  async searchJobs(
    guildId: string,
    filters?: {
      jobType?: string;
      workMode?: string;
      industry?: string;
      experienceLevel?: string;
      location?: string;
      skills?: string[];
    },
    searchTerm?: string,
    userId?: string,
    limit: number = 20
  ): Promise<JobWithStats[]> {
    try {
      let whereConditions = [
        eq(jobPostings.guildId, guildId),
        eq(jobPostings.isActive, true),
        gte(jobPostings.expiresAt, new Date())
      ];
      
      if (filters?.jobType) {
        whereConditions.push(eq(jobPostings.jobType, filters.jobType));
      }
      if (filters?.workMode) {
        whereConditions.push(eq(jobPostings.workMode, filters.workMode));
      }
      if (filters?.industry) {
        whereConditions.push(eq(jobPostings.industry, filters.industry));
      }
      if (filters?.experienceLevel) {
        whereConditions.push(eq(jobPostings.experienceLevel, filters.experienceLevel));
      }
      if (filters?.location) {
        whereConditions.push(ilike(jobPostings.location, `%${filters.location}%`));
      }

      if (searchTerm) {
        whereConditions.push(sql`(
          ${jobPostings.title} ILIKE ${'%' + searchTerm + '%'} OR
          ${jobPostings.description} ILIKE ${'%' + searchTerm + '%'} OR
          ${jobPostings.company} ILIKE ${'%' + searchTerm + '%'}
        )`);
      }

      if (filters?.skills && filters.skills.length > 0) {
        whereConditions.push(sql`(
          ${jobPostings.requiredSkills}::jsonb ?| array[${filters.skills.map((s: any) => `'${s}'`).join(',')}] OR
          ${jobPostings.preferredSkills}::jsonb ?| array[${filters.skills.map((s: any) => `'${s}'`).join(',')}]
        )`);
      }

      const jobs = await this.db
        .select({
          id: jobPostings.id,
          employerId: jobPostings.employerId,
          title: jobPostings.title,
          description: jobPostings.description,
          company: jobPostings.company,
          location: jobPostings.location,
          jobType: jobPostings.jobType,
          workMode: jobPostings.workMode,
          industry: jobPostings.industry,
          experienceLevel: jobPostings.experienceLevel,
          requiredSkills: jobPostings.requiredSkills,
          preferredSkills: jobPostings.preferredSkills,
          salaryMin: jobPostings.salaryMin,
          salaryMax: jobPostings.salaryMax,
          currency: jobPostings.currency,
          benefits: jobPostings.benefits,
          applicationUrl: jobPostings.applicationUrl,
          applicationEmail: jobPostings.applicationEmail,
          expiresAt: jobPostings.expiresAt,
          isActive: jobPostings.isActive,
          applicationCount: jobPostings.applicationCount,
          guildId: jobPostings.guildId,
          tags: jobPostings.tags,
          createdAt: jobPostings.createdAt,
          updatedAt: jobPostings.updatedAt,
          deletedAt: jobPostings.deletedAt,
          isApplied: userId ? sql<boolean>`CASE WHEN ${jobApplications.id} IS NOT NULL THEN true ELSE false END` : sql<boolean>`false`
        })
        .from(jobPostings)
        .leftJoin(
          jobApplications,
          userId ? and(
            eq(jobApplications.jobId, jobPostings.id),
            eq(jobApplications.applicantId, userId)
          ) : undefined
        )
        .where(and(...whereConditions))
        .orderBy(desc(jobPostings.createdAt))
        .limit(limit);

      return jobs as JobWithStats[];
    } catch (error) {
      this.logger.error('Failed to search jobs:', error);
      throw error;
    }
  }

  async applyToJob(
    jobId: number,
    applicantId: string,
    applicationData: {
      coverLetter?: string;
      resumeUrl?: string;
      portfolioUrl?: string;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already applied
      const existing = await this.db
        .select()
        .from(jobApplications)
        .where(and(
          eq(jobApplications.jobId, jobId),
          eq(jobApplications.applicantId, applicantId)
        ))
        .limit(1);

      if (existing.length > 0) {
        return { success: false, message: 'You have already applied to this job!' };
      }

      await this.db.insert(jobApplications).values({
        jobId,
        applicantId,
        ...applicationData
      });

      // Update application count
      await this.db
        .update(jobPostings)
        .set({
          applicationCount: sql`${jobPostings.applicationCount} + 1`
        } as any)
        .where(eq(jobPostings.id, jobId));

      this.logger.log(`User ${applicantId} applied to job ${jobId}`);
      return { success: true, message: 'Application submitted successfully!' };
    } catch (error) {
      this.logger.error('Failed to apply to job:', error);
      return { success: false, message: 'Failed to submit application. Please try again.' };
    }
  }

  async getJobApplications(jobId: number): Promise<JobApplication[]> {
    try {
      return await this.db
        .select()
        .from(jobApplications)
        .where(eq(jobApplications.jobId, jobId))
        .orderBy(desc(jobApplications.createdAt));
    } catch (error) {
      this.logger.error('Failed to get job applications:', error);
      throw error;
    }
  }

  // Business Opportunity Management
  async createOpportunity(opportunityData: Omit<NewBusinessOpportunity, 'id' | 'createdAt' | 'updatedAt'>): Promise<BusinessOpportunity> {
    try {
      const [opportunity] = await this.db.insert(businessOpportunities)
        .values(opportunityData)
        .returning();
      
      this.logger.log(`Created business opportunity: ${opportunity.title} (${opportunity.id})`);
      return opportunity;
    } catch (error) {
      this.logger.error('Failed to create opportunity:', error);
      throw error;
    }
  }

  async searchOpportunities(
    guildId: string,
    filters?: {
      type?: string;
      industry?: string;
      location?: string;
      isRemote?: boolean;
    },
    searchTerm?: string,
    limit: number = 20
  ): Promise<BusinessOpportunity[]> {
    try {
      let whereConditions = [
        eq(businessOpportunities.guildId, guildId),
        eq(businessOpportunities.isActive, true),
        gte(businessOpportunities.expiresAt, new Date())
      ];
      
      if (filters?.type) {
        whereConditions.push(eq(businessOpportunities.type, filters.type));
      }
      if (filters?.industry) {
        whereConditions.push(eq(businessOpportunities.industry, filters.industry));
      }
      if (filters?.location) {
        whereConditions.push(ilike(businessOpportunities.location, `%${filters.location}%`));
      }
      if (filters?.isRemote !== undefined) {
        whereConditions.push(eq(businessOpportunities.isRemote, filters.isRemote));
      }

      if (searchTerm) {
        whereConditions.push(sql`(
          ${businessOpportunities.title} ILIKE ${'%' + searchTerm + '%'} OR
          ${businessOpportunities.description} ILIKE ${'%' + searchTerm + '%'}
        )`);
      }

      return await this.db
        .select()
        .from(businessOpportunities)
        .where(and(...whereConditions))
        .orderBy(desc(businessOpportunities.createdAt))
        .limit(limit);
    } catch (error) {
      this.logger.error('Failed to search opportunities:', error);
      throw error;
    }
  }

  async expressInterest(
    opportunityId: number,
    userId: string,
    message?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Check if already expressed interest
      const existing = await this.db
        .select()
        .from(opportunityInterests)
        .where(and(
          eq(opportunityInterests.opportunityId, opportunityId),
          eq(opportunityInterests.userId, userId)
        ))
        .limit(1);

      if (existing.length > 0) {
        return { success: false, message: 'You have already expressed interest in this opportunity!' };
      }

      await this.db.insert(opportunityInterests).values({
        opportunityId,
        userId,
        message
      } as any);

      // Update interest count
      await this.db
        .update(businessOpportunities)
        .set({
          interestCount: sql`${businessOpportunities.interestCount} + 1`
        } as any)
        .where(eq(businessOpportunities.id, opportunityId));

      this.logger.log(`User ${userId} expressed interest in opportunity ${opportunityId}`);
      return { success: true, message: 'Interest expressed successfully!' };
    } catch (error) {
      this.logger.error('Failed to express interest:', error);
      return { success: false, message: 'Failed to express interest. Please try again.' };
    }
  }

  // Analytics
  async getNetworkingMetrics(guildId: string): Promise<NetworkingMetrics> {
    try {
      const [profileStats] = await this.db
        .select({
          totalProfiles: sql<number>`COUNT(*)`
        })
        .from(professionalProfiles)
        .where(eq(professionalProfiles.guildId, guildId));

      const [connectionStats] = await this.db
        .select({
          totalConnections: sql<number>`COUNT(*)`
        })
        .from(professionalConnections)
        .where(eq(professionalConnections.status, 'accepted'));

      const [jobStats] = await this.db
        .select({
          totalJobs: sql<number>`COUNT(*)`,
          totalApplications: sql<number>`SUM(application_count)`,
          activeRecruiters: sql<number>`COUNT(DISTINCT employer_id)`
        })
        .from(jobPostings)
        .where(eq(jobPostings.guildId, guildId));

      const [opportunityStats] = await this.db
        .select({
          totalOpportunities: sql<number>`COUNT(*)`
        })
        .from(businessOpportunities)
        .where(eq(businessOpportunities.guildId, guildId));

      const popularSkills = await this.db
        .select({
          skill: skillEndorsements.skill,
          count: sql<number>`COUNT(*)`
        })
        .from(skillEndorsements)
        .innerJoin(professionalProfiles, eq(skillEndorsements.profileId, professionalProfiles.userId))
        .where(eq(professionalProfiles.guildId, guildId))
        .groupBy(skillEndorsements.skill)
        .orderBy(desc(sql`COUNT(*)`))
        .limit(10);

      const industryBreakdown = await this.db
        .select({
          industry: professionalProfiles.industry,
          count: sql<number>`COUNT(*)`
        })
        .from(professionalProfiles)
        .where(eq(professionalProfiles.guildId, guildId))
        .groupBy(professionalProfiles.industry)
        .orderBy(desc(sql`COUNT(*)`))
        .limit(10);

      return {
        totalProfiles: profileStats.totalProfiles || 0,
        totalConnections: connectionStats.totalConnections || 0,
        totalJobs: jobStats.totalJobs || 0,
        totalApplications: jobStats.totalApplications || 0,
        totalOpportunities: opportunityStats.totalOpportunities || 0,
        activeRecruiters: jobStats.activeRecruiters || 0,
        popularSkills: popularSkills.map((s: any) => ({ skill: s.skill, count: s.count })),
        industryBreakdown: industryBreakdown.map((i: any) => ({ industry: i.industry, count: i.count }))
      };
    } catch (error) {
      this.logger.error('Failed to get networking metrics:', error);
      throw error;
    }
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {
      const [existingUser] = await this.db
        .select()
        .from(users)
        .where(eq(users.discordId, discordId))
        .limit(1);

      if (!existingUser) {
        await this.db.insert(users).values({
          discordId,
          username,
          isActive: true,
          lastActivityAt: new Date(),
          preferences: {},
          profile: {}
        } as any);
        
        this.logger.log(`Created new user: ${username} (${discordId})`);
      } else if (existingUser.username !== username) {
        await this.db
          .update(users)
          .set({ 
            username,
            lastActivityAt: new Date()
          } as any)
          .where(eq(users.discordId, discordId));
      }
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error;
    }
  }
}