
import { BaseEntity, CreateEntity, UpdateEntity } from '../types/base.interface';

export type AIProvider = 'openai' | 'anthropic' | 'google' | 'azure' | 'exa' | 'custom';

export interface APIKeyConfig {
  provider: AIProvider;
  keyName: string;
  displayName: string;
  isActive: boolean;
  lastUsed?: string;
  usageCount: number;
  maxTokens?: number;
  dailyLimit?: number;
  features: string[];
  selectedModel?: string; // Primary model to use with this API key
  modelPreferences?: {
    textGeneration?: string;
    codeGeneration?: string;
    imageGeneration?: string;
    embeddings?: string;
  };
}

export interface APIKeyValidation {
  isValid: boolean;
  lastChecked: string;
  errorMessage?: string;
  supportedModels?: string[];
  quotaInfo?: {
    used: number;
    limit: number;
    resetDate: string;
  };
}

/**
 * User API Key entity interface - Redis compatible
 */
export interface UserApiKey extends BaseEntity {
  userId: string;
  guildId: string;
  provider: AIProvider;
  keyName: string;
  encryptedKey: string;
  config: APIKeyConfig;
  validation?: APIKeyValidation;
  isActive: boolean;
  isDefault: boolean;
  lastUsedAt?: Date;
  expiresAt?: Date;
}

/**
 * Type for creating a new user API key
 */
export type CreateUserApiKey = CreateEntity<UserApiKey>;

/**
 * Type for updating a user API key
 */
export type UpdateUserApiKey = UpdateEntity<UserApiKey>;

/**
 * Legacy compatibility types
 */
export type NewUserApiKey = CreateUserApiKey;

/**
 * Redis key patterns for user API keys entity
 */
export const UserApiKeyKeys = {
  primary: (id: string) => `user_api_key:${id}`,
  byUser: (userId: string) => `user_api_keys:user:${userId}`,
  byGuild: (guildId: string) => `user_api_keys:guild:${guildId}`,
  byProvider: (provider: AIProvider) => `user_api_keys:provider:${provider}`,
  active: () => 'user_api_keys:active',
  pattern: 'user_api_key:*',
  index: {
    userId: 'idx:user_api_key:user_id',
    guildId: 'idx:user_api_key:guild_id',
    provider: 'idx:user_api_key:provider',
    isActive: 'idx:user_api_key:is_active',
    isDefault: 'idx:user_api_key:is_default',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const userApiKeys = {
  $inferSelect: {} as UserApiKey,
  $inferInsert: {} as CreateUserApiKey
} as const;